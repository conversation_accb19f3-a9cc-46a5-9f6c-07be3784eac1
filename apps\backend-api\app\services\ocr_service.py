# import pytesseract  # Removed - using multimodal LLM/external API instead
from PIL import Image
from pdf2image import convert_from_path
from pypdf import PdfReader  # Replaced PyPDF2 with pypdf
import io
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class OCRService:
    """Service for extracting text from PDFs and preparing images for LLM processing"""

    def __init__(self):
        pass

    def process_file_for_llm(self, file_data_base64: str, file_type: str) -> Tuple[Optional[str], Optional[str]]:
        """Process file for LLM - returns (text_content, image_data_base64)

        For PDFs: Extract text and return (extracted_text, None)
        For images: Return (None, base64_image_data)

        Args:
            file_data_base64: Base64 encoded file data
            file_type: File type (pdf, jpg, png, etc.)

        Returns:
            tuple: (text_content, image_data_base64) - one will be None
        """
        try:
            if file_type.lower() == 'pdf':
                text_content = self._extract_text_from_pdf_base64(file_data_base64)
                return (text_content, None)
            elif file_type.lower() in ['png', 'jpg', 'jpeg']:
                # För bilder returnerar vi base64 data direkt för LLM
                return (None, file_data_base64)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
        except Exception as e:
            logger.error(f"Error processing file of type {file_type}: {e}")
            raise

    def _extract_text_from_pdf_base64(self, file_data_base64: str) -> str:
        """Extract text from PDF given base64 data"""
        import base64
        import tempfile
        import os

        try:
            # Decode base64 to bytes
            pdf_bytes = base64.b64decode(file_data_base64)

            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_bytes)
                temp_file_path = temp_file.name

            try:
                # Extract text using existing method
                text = self._extract_from_pdf(temp_file_path)
                return text
            finally:
                # Clean up temporary file
                os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error extracting text from PDF base64: {e}")
            raise
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""

        # First try to extract text directly (for PDFs with selectable text)
        try:
            pdf_reader = PdfReader(file_path)
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text.strip():
                    text += page_text + "\n"

            # If we got meaningful text, return it
            if len(text.strip()) > 10:  # Lower threshold
                logger.info(f"Extracted text directly from PDF: {len(text)} characters")
                return text.strip()
            else:
                logger.info("PDF appears to be image-based or has minimal text")

        except Exception as e:
            logger.warning(f"Direct PDF text extraction failed: {e}")

        # If direct extraction failed or yielded little text, try external OCR
        try:
            logger.info("Attempting external OCR for PDF")
            return self._external_ocr_pdf(file_path)
        except Exception as e:
            logger.warning(f"External OCR failed: {e}")
            # Return whatever text we managed to extract, even if minimal
            if text.strip():
                logger.info("Returning partial text extraction from PDF")
                return text.strip()
            else:
                logger.warning("No text could be extracted from PDF")
                return "PDF innehåller ingen läsbar text eller kräver OCR som inte är tillgängligt."
    
    def _external_ocr_pdf(self, file_path: str) -> str:
        """Extract text from PDF using external OCR API or multimodal LLM"""
        try:
            # Try to convert PDF pages to images for external processing
            # This requires poppler to be installed
            images = convert_from_path(file_path, dpi=300)
            logger.info(f"Converted PDF to {len(images)} images for OCR processing")

            # TODO: Implement external OCR API call or multimodal LLM
            # For now, return a placeholder message
            logger.warning("External OCR not yet implemented - returning placeholder")
            return "PDF kräver OCR bearbetning som inte är implementerat än."

            # Example implementation structure:
            # text = ""
            # for i, image in enumerate(images):
            #     logger.debug(f"Processing page {i+1} of PDF with external OCR")
            #     # Save image temporarily
            #     temp_image_path = f"/tmp/page_{i}.jpg"
            #     image.save(temp_image_path, "JPEG")
            #
            #     # Call external OCR API or multimodal LLM
            #     page_text = await self._call_external_ocr_api(temp_image_path)
            #     text += page_text + "\n"
            #
            #     # Cleanup temp file
            #     os.unlink(temp_image_path)
            #
            # logger.info(f"External OCR extracted {len(text)} characters from PDF")
            # return text.strip()

        except Exception as e:
            logger.error(f"External OCR PDF extraction failed: {e}")
            raise
    
    def _extract_from_image(self, file_path: str) -> str:
        """Extract text from image file using external OCR API or multimodal LLM"""
        try:
            # Open and preprocess image
            image = Image.open(file_path)

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Enhance image for better OCR results
            image = self._preprocess_image(image)

            # TODO: Implement external OCR API call or multimodal LLM
            # For now, return a placeholder message
            logger.warning("External OCR not yet implemented - returning placeholder")
            return "OCR text extraction not yet implemented. Please implement external OCR API or multimodal LLM integration."

            # Example implementation:
            # text = await self._call_external_ocr_api(file_path)
            # logger.info(f"External OCR extracted {len(text)} characters from image")
            # return text.strip()

        except Exception as e:
            logger.error(f"Image OCR extraction failed: {e}")
            raise
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image to improve OCR accuracy"""
        try:
            # Resize if image is too small
            width, height = image.size
            if width < 1000 or height < 1000:
                scale_factor = max(1000 / width, 1000 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale for better OCR
            image = image.convert('L')
            
            # Enhance contrast
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            return image
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}")
            return image  # Return original if preprocessing fails
    
    def validate_extracted_text(self, text: str) -> bool:
        """Validate that extracted text looks like an invoice"""
        if not text or len(text.strip()) < 20:
            return False
        
        # Look for common invoice keywords
        invoice_keywords = [
            'invoice', 'faktura', 'bill', 'receipt', 'kvitto',
            'amount', 'belopp', 'total', 'summa', 'date', 'datum',
            'supplier', 'leverantör', 'customer', 'kund'
        ]
        
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in invoice_keywords if keyword in text_lower)
        
        # Should have at least 2 invoice-related keywords
        return keyword_count >= 2


# Global service instance
_ocr_service = None


def get_ocr_service() -> OCRService:
    """Get singleton OCR service instance"""
    global _ocr_service
    if _ocr_service is None:
        _ocr_service = OCRService()
    return _ocr_service
